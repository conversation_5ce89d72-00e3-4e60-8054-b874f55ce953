================================================================================
📋 GAC Agent Traveler 冒烟测试报告
================================================================================
🎯 测试目标: http://127.0.0.1:8080
⏰ 测试时间: 2025-09-02 16:06:50
📊 测试统计:
   总测试数: 3
   通过测试: 1
   失败测试: 2
   成功率: 33.3%
   总耗时: 121.07秒

📝 详细测试结果:
--------------------------------------------------------------------------------
✅ PASS | smoke_test_01 | 15.66s
❌ FAIL | smoke_test_02 | 105.21s
❌ FAIL | smoke_test_03 | 0.20s
     错误: Traceback (most recent call last):
  File "/data/lql/agents/gac-agent-traveler/test/smoke_test_03.py", line 18, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'


⚠️  部分冒烟测试失败，请检查服务状态
================================================================================