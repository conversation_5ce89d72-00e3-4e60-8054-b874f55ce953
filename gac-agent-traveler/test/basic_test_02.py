#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent Traveler 基础测试程序 02
测试Python环境和基本模块导入
"""

import sys
import time

def test_python_environment():
    """测试Python环境"""
    print("=" * 60)
    print("🧪 GAC Agent Traveler 基础测试 02")
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 当前工作目录: {sys.path[0]}")
    
    # 测试基本模块导入
    modules_to_test = [
        "requests",
        "json", 
        "time",
        "sys",
        "os"
    ]
    
    print("\n📦 测试模块导入:")
    failed_imports = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name}: 导入成功")
        except ImportError as e:
            print(f"   ❌ {module_name}: 导入失败 - {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n❌ 模块导入失败: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ 所有模块导入成功")
        return True

def test_network_connectivity():
    """测试网络连通性"""
    print("\n🌐 测试网络连通性:")
    
    try:
        import requests
        
        # 测试基本网络连接
        test_url = "http://httpbin.org/get"
        print(f"   测试URL: {test_url}")
        
        response = requests.get(test_url, timeout=10)
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 网络连接正常")
            return True
        else:
            print("   ❌ 网络连接异常")
            return False
            
    except ImportError:
        print("   ❌ requests模块未安装")
        return False
    except Exception as e:
        print(f"   ❌ 网络测试失败: {str(e)}")
        return False

def test_local_service():
    """测试本地服务连接"""
    print("\n🔧 测试本地服务连接:")
    
    try:
        import requests
        
        # 测试本地服务
        base_url = "http://127.0.0.1:8080"
        print(f"   服务地址: {base_url}")
        
        try:
            response = requests.get(base_url, timeout=5)
            print(f"   连接状态码: {response.status_code}")
            
            if response.status_code in [200, 404]:
                print("   ✅ 本地服务可连接")
                
                # 尝试健康检查
                health_url = f"{base_url}/gac-agent-traveler/v1/health"
                try:
                    health_response = requests.get(health_url, timeout=5)
                    print(f"   健康检查状态码: {health_response.status_code}")
                    
                    if health_response.status_code == 200:
                        health_data = health_response.json()
                        print(f"   健康检查响应: {health_data}")
                        print("   ✅ 服务健康检查通过")
                        return True
                    else:
                        print("   ⚠️  服务连接成功但健康检查失败")
                        return False
                except Exception as e:
                    print(f"   ⚠️  健康检查失败: {str(e)}")
                    return False
            else:
                print("   ❌ 本地服务连接异常")
                return False
                
        except requests.exceptions.ConnectionError:
            print("   ❌ 无法连接到本地服务")
            print("   💡 请确保服务已启动: python main.py")
            return False
            
    except ImportError:
        print("   ❌ requests模块未安装")
        return False
    except Exception as e:
        print(f"   ❌ 本地服务测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    # 测试Python环境
    env_ok = test_python_environment()
    
    # 测试网络连通性
    network_ok = test_network_connectivity()
    
    # 测试本地服务
    service_ok = test_local_service()
    
    print("\n📊 测试总结:")
    print(f"   Python环境: {'✅ 正常' if env_ok else '❌ 异常'}")
    print(f"   网络连接: {'✅ 正常' if network_ok else '❌ 异常'}")
    print(f"   本地服务: {'✅ 正常' if service_ok else '❌ 异常'}")
    
    if env_ok and network_ok and service_ok:
        print("\n🎉 所有基础测试通过！环境配置正常")
        print("=" * 60)
        return True
    else:
        print("\n⚠️  部分基础测试失败，请检查环境配置")
        print("=" * 60)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
