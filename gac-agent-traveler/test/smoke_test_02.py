#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冒烟测试 02 - GAC Agent Traveler 流式响应和边界测试
测试服务地址: http://127.0.0.1:8080

冒烟测试目标：
1. 验证流式响应功能
2. 验证不同搜索引擎配置
3. 验证参数边界值处理
4. 验证并发请求处理能力
"""

import requests
import time
import json
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any


class SmokeTest02:
    """GAC Agent Traveler 流式响应和边界测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")
    
    def test_stream_response(self) -> bool:
        """测试1: 流式响应测试"""
        test_name = "流式响应测试"
        start_time = time.time()
        
        payload = {
            "query": "上海有什么特色美食？",
            "stream": True,  # 启用流式响应
            "k": 5,
            "engine": "tencent",
            "detect": False,
            "location": {
                "lat": "31.2304",
                "lon": "121.4737"
            },
            "user_info": {
                "car_id": "stream_test_car",
                "user_id": "stream_test_user",
                "category": ["美食"]
            }
        }
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=30
            )
            
            if response.status_code == 200:
                chunks_received = 0
                total_data = ""
                
                # 读取流式数据
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        chunks_received += 1
                        total_data += chunk
                        # 限制读取时间，避免无限等待
                        if time.time() - start_time > 25:
                            break
                
                duration = time.time() - start_time
                
                if chunks_received > 0:
                    self.log_test_result(test_name, True, f"流式响应正常 (接收{chunks_received}个数据块)", duration)
                    return True
                else:
                    self.log_test_result(test_name, False, "未接收到流式数据", duration)
                    return False
            else:
                duration = time.time() - start_time
                self.log_test_result(test_name, False, f"流式请求失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"流式响应异常: {str(e)}", duration)
            return False
    
    def test_different_engines(self) -> bool:
        """测试2: 不同搜索引擎测试"""
        test_name = "不同搜索引擎测试"
        start_time = time.time()
        
        engines = ["tencent", "bing", "serper"]
        successful_engines = []
        
        base_payload = {
            "query": "深圳有什么好玩的地方？",
            "stream": False,
            "k": 3,
            "detect": False,
            "location": {
                "lat": "22.5431",
                "lon": "114.0579"
            },
            "user_info": {
                "car_id": "engine_test_car",
                "user_id": "engine_test_user",
                "category": ["旅游"]
            }
        }
        
        for engine in engines:
            try:
                payload = base_payload.copy()
                payload["engine"] = engine
                
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=15
                )
                
                if response.status_code == 200:
                    successful_engines.append(engine)
                    
            except Exception as e:
                print(f"   引擎 {engine} 测试异常: {str(e)}")
        
        duration = time.time() - start_time
        
        if successful_engines:
            self.log_test_result(test_name, True, f"成功的引擎: {', '.join(successful_engines)}", duration)
            return True
        else:
            self.log_test_result(test_name, False, "所有搜索引擎都失败", duration)
            return False
    
    def test_parameter_boundaries(self) -> bool:
        """测试3: 参数边界值测试"""
        test_name = "参数边界值测试"
        start_time = time.time()
        
        test_cases = [
            {"k": 1, "desc": "最小k值"},
            {"k": 20, "desc": "较大k值"},
            {"query": "a", "desc": "最短查询"},
            {"query": "这是一个非常长的查询语句" * 10, "desc": "超长查询"},
        ]
        
        base_payload = {
            "query": "杭州西湖怎么样？",
            "stream": False,
            "k": 5,
            "engine": "tencent",
            "detect": False,
            "location": {
                "lat": "30.2741",
                "lon": "120.1551"
            },
            "user_info": {
                "car_id": "boundary_test_car",
                "user_id": "boundary_test_user",
                "category": ["旅游"]
            }
        }
        
        successful_cases = 0
        
        for case in test_cases:
            try:
                payload = base_payload.copy()
                payload.update({k: v for k, v in case.items() if k != "desc"})
                
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=20
                )
                
                if response.status_code == 200:
                    successful_cases += 1
                    print(f"   ✓ {case['desc']}: 成功")
                else:
                    print(f"   ✗ {case['desc']}: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        if successful_cases >= len(test_cases) // 2:  # 至少一半成功
            self.log_test_result(test_name, True, f"边界测试通过 ({successful_cases}/{len(test_cases)})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"边界测试失败 ({successful_cases}/{len(test_cases)})", duration)
            return False
    
    def test_concurrent_requests(self) -> bool:
        """测试4: 并发请求测试"""
        test_name = "并发请求测试"
        start_time = time.time()
        
        def make_request(request_id: int) -> bool:
            """发送单个请求"""
            payload = {
                "query": f"广州有什么好吃的？请求{request_id}",
                "stream": False,
                "k": 3,
                "engine": "tencent",
                "detect": False,
                "location": {
                    "lat": "23.1291",
                    "lon": "113.2644"
                },
                "user_info": {
                    "car_id": f"concurrent_test_car_{request_id}",
                    "user_id": f"concurrent_test_user_{request_id}",
                    "category": ["美食"]
                }
            }
            
            try:
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=25
                )
                return response.status_code == 200
            except:
                return False
        
        # 并发发送5个请求
        concurrent_count = 5
        successful_requests = 0
        
        with ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            futures = [executor.submit(make_request, i) for i in range(concurrent_count)]
            
            for future in as_completed(futures):
                if future.result():
                    successful_requests += 1
        
        duration = time.time() - start_time
        
        if successful_requests >= concurrent_count // 2:  # 至少一半成功
            self.log_test_result(test_name, True, f"并发测试通过 ({successful_requests}/{concurrent_count})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"并发测试失败 ({successful_requests}/{concurrent_count})", duration)
            return False
    
    def run_smoke_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("=" * 80)
        print("🧪 GAC Agent Traveler 冒烟测试 02 开始")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 执行测试序列
        tests = [
            self.test_stream_response,
            self.test_different_engines,
            self.test_parameter_boundaries,
            self.test_concurrent_requests
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)
        
        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests
        
        print("📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if overall_success:
            print("🎉 冒烟测试全部通过！服务高级功能正常")
        else:
            print("⚠️  部分冒烟测试失败，请检查服务配置")
        
        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    smoke_test = SmokeTest02(base_url)
    success = smoke_test.run_smoke_tests()
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
