#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent Traveler 合并冒烟测试
测试服务地址: http://127.0.0.1:8080

测试内容：
1. 基础功能测试
2. 流式响应测试
3. 搜索引擎测试
4. 参数边界测试
5. 性能测试（5轮）
6. 错误恢复测试
"""

import requests
import time
import json
import sys
from typing import List, Dict, Any
from statistics import mean, median


class CombinedSmokeTest:
    """GAC Agent Traveler 合并冒烟测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/gac-agent-traveler/v1/health"
        self.ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")

    def extract_timing_info(self, response_data: Dict[str, Any]) -> Dict[str, float]:
        """从响应数据中提取时间信息"""
        timing_info = {}

        # 尝试从不同可能的位置提取时间信息
        if isinstance(response_data, dict):
            # 检查是否有直接的时间字段
            time_fields = [
                "t1-loc", "t2-mem", "t3-pre-intent", "t3-intent",
                "t4-src", "t5-fetch", "t6-mft", "t7-sft",
                "t8-list", "t9-brief", "t10-detail", "t-acft", "TTFT"
            ]

            for field in time_fields:
                if field in response_data:
                    try:
                        timing_info[field] = float(response_data[field])
                    except (ValueError, TypeError):
                        pass

            # 检查嵌套的时间信息
            if "time_info" in response_data and isinstance(response_data["time_info"], dict):
                for field in time_fields:
                    if field in response_data["time_info"]:
                        try:
                            timing_info[field] = float(response_data["time_info"][field])
                        except (ValueError, TypeError):
                            pass

        return timing_info

    def format_timing_summary(self, timing_info: Dict[str, float]) -> str:
        """格式化时间信息摘要"""
        if not timing_info:
            return ""

        # 计算各模块耗时（按照AI_Traveler_Test_For_Dify.py的逻辑）
        summary_parts = []

        # 主要时间节点
        key_timings = {
            "位置": timing_info.get("t1-loc", 0),
            "画像": timing_info.get("t2-mem", 0) - timing_info.get("t1-loc", 0),
            "意图": timing_info.get("t3-intent", 0) - timing_info.get("t3-pre-intent", 0),
            "搜索": timing_info.get("t4-src", 0) - timing_info.get("t3-intent", 0),
            "爬虫": timing_info.get("t5-fetch", 0) - timing_info.get("t4-src", 0),
            "总结": timing_info.get("t6-mft", 0) - timing_info.get("t5-fetch", 0),
        }

        for name, duration in key_timings.items():
            if duration > 0:
                summary_parts.append(f"{name}:{duration:.2f}s")

        return f"[{' '.join(summary_parts)}]" if summary_parts else ""
        
    def test_service_connectivity(self) -> bool:
        """测试1: 服务连通性测试"""
        test_name = "服务连通性测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.base_url, timeout=25)
            duration = time.time() - start_time
            
            # FastAPI默认会返回404，但能连通说明服务正常
            if response.status_code in [200, 404]:
                self.log_test_result(test_name, True, f"服务连通正常 (状态码: {response.status_code})", duration)
                return True
            else:
                self.log_test_result(test_name, False, f"服务响应异常 (状态码: {response.status_code})", duration)
                return False
                
        except requests.exceptions.ConnectionError:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "无法连接到服务", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"连接异常: {str(e)}", duration)
            return False
    
    def test_health_endpoint(self) -> bool:
        """测试2: 健康检查端点测试"""
        test_name = "健康检查端点测试"
        start_time = time.time()
        
        try:
            response = requests.get(self.health_url, timeout=25)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    health_data = response.json()
                    if health_data.get("status") == "ok":
                        version = health_data.get("version", "未知")
                        self.log_test_result(test_name, True, f"健康检查通过 (版本: {version})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"健康状态异常: {health_data}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "健康检查响应格式错误", duration)
                    return False
            else:
                self.log_test_result(test_name, False, f"健康检查失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"健康检查异常: {str(e)}", duration)
            return False
    
    def test_ai_traveler_basic_request(self) -> bool:
        """测试3: AI旅行家基础请求测试"""
        test_name = "AI旅行家基础请求测试"
        start_time = time.time()

        payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": "今天吃什么",
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                timeout=25
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if isinstance(result, dict):
                        self.log_test_result(test_name, True, f"AI旅行家请求成功 (响应长度: {len(str(result))})", duration)
                        return True
                    else:
                        self.log_test_result(test_name, False, f"响应格式异常: {type(result)}", duration)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, False, "响应JSON格式错误", duration)
                    return False
            else:
                error_msg = response.text[:200] if response.text else "无错误信息"
                self.log_test_result(test_name, False, f"请求失败 (状态码: {response.status_code}) - {error_msg}", duration)
                return False
                
        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, "请求超时", duration)
            return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"请求异常: {str(e)}", duration)
            return False
    
    def test_stream_response(self) -> bool:
        """测试4: 流式响应测试"""
        test_name = "流式响应测试"
        start_time = time.time()
        
        payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": "上海有什么特色美食？",
            "stream": True,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=25
            )
            
            if response.status_code == 200:
                chunks_received = 0
                
                # 读取流式数据
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        chunks_received += 1
                        # 限制读取时间，避免无限等待
                        if time.time() - start_time > 20:
                            break
                
                duration = time.time() - start_time
                
                if chunks_received > 0:
                    self.log_test_result(test_name, True, f"流式响应正常 (接收{chunks_received}个数据块)", duration)
                    return True
                else:
                    self.log_test_result(test_name, False, "未接收到流式数据", duration)
                    return False
            else:
                duration = time.time() - start_time
                self.log_test_result(test_name, False, f"流式请求失败 (状态码: {response.status_code})", duration)
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"流式响应异常: {str(e)}", duration)
            return False
    
    def test_different_engines(self) -> bool:
        """测试5: 不同搜索引擎测试"""
        test_name = "不同搜索引擎测试"
        start_time = time.time()
        
        engines = ["tencent", "bing", "serper"]
        successful_engines = []
        
        base_payload = {
            "detect": False,
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": "深圳有什么好玩的地方？",
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        for engine in engines:
            try:
                payload = base_payload.copy()
                payload["engine"] = engine
                
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=25
                )
                
                if response.status_code == 200:
                    successful_engines.append(engine)
                    print(f"   ✓ 引擎 {engine}: 成功")
                else:
                    print(f"   ✗ 引擎 {engine}: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   ✗ 引擎 {engine}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        if successful_engines:
            self.log_test_result(test_name, True, f"成功的引擎: {', '.join(successful_engines)}", duration)
            return True
        else:
            self.log_test_result(test_name, False, "所有搜索引擎都失败", duration)
            return False
    
    def test_parameter_boundaries(self) -> bool:
        """测试6: 参数边界值测试"""
        test_name = "参数边界值测试"
        start_time = time.time()
        
        test_cases = [
            {"query": "北京游玩", "desc": "最短查询"},
            {"query": "我想去北京玩请问哪里好玩，最好是休闲娱乐的地方", "desc": "长查询"},
        ]
        
        base_payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": "杭州西湖怎么样？",
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        successful_cases = 0
        
        for case in test_cases:
            try:
                payload = base_payload.copy()
                payload.update({k: v for k, v in case.items() if k != "desc"})
                
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=25
                )
                
                if response.status_code == 200:
                    successful_cases += 1
                    print(f"   ✓ {case['desc']}: 成功")
                else:
                    print(f"   ✗ {case['desc']}: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   ✗ {case['desc']}: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        if successful_cases >= len(test_cases) // 2:
            self.log_test_result(test_name, True, f"边界测试通过 ({successful_cases}/{len(test_cases)})", duration)
            return True
        else:
            self.log_test_result(test_name, False, f"边界测试失败 ({successful_cases}/{len(test_cases)})", duration)
            return False
    
    def test_response_time_performance(self) -> bool:
        """测试7: 响应时间性能测试（5轮）"""
        test_name = "响应时间性能测试"
        start_time = time.time()
        
        payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": "成都有什么特色景点？",
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        response_times = []
        successful_requests = 0
        test_rounds = 5
        
        print(f"   执行 {test_rounds} 轮性能测试...")
        
        for i in range(test_rounds):
            try:
                req_start = time.time()
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=25
                )
                req_duration = time.time() - req_start
                
                if response.status_code == 200:
                    response_times.append(req_duration)
                    successful_requests += 1

                    # 解析响应中的时间信息
                    try:
                        result = response.json()
                        timing_info = self.extract_timing_info(result)
                        timing_summary = self.format_timing_summary(timing_info)
                        print(f"   第{i+1}轮: {req_duration:.2f}s {timing_summary}")
                    except:
                        print(f"   第{i+1}轮: {req_duration:.2f}s")
                else:
                    print(f"   第{i+1}轮: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   第{i+1}轮: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        if response_times:
            avg_time = mean(response_times)
            median_time = median(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            # 性能标准：平均响应时间 < 15秒，成功率 > 60%
            success_rate = successful_requests / test_rounds
            performance_ok = avg_time < 15.0 and success_rate > 0.6
            
            message = f"平均:{avg_time:.2f}s 中位数:{median_time:.2f}s 最大:{max_time:.2f}s 最小:{min_time:.2f}s 成功率:{success_rate:.1%}"
            self.log_test_result(test_name, performance_ok, message, duration)
            return performance_ok
        else:
            self.log_test_result(test_name, False, "所有请求都失败", duration)
            return False
    
    def test_error_recovery(self) -> bool:
        """测试8: 错误恢复能力测试"""
        test_name = "错误恢复能力测试"
        start_time = time.time()
        
        # 先发送一个错误请求
        invalid_payload = {
            "query": "",  # 空查询
            "invalid_field": "test"
        }
        
        try:
            # 发送无效请求
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=invalid_payload,
                timeout=25
            )
            print(f"   无效请求响应: {response.status_code}")
        except Exception as e:
            print(f"   无效请求异常: {str(e)}")
        
        # 短暂等待
        time.sleep(1)
        
        # 然后发送正常请求，测试服务是否能恢复
        valid_payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": "天津有什么特色小吃？",
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        recovery_successful = False
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=valid_payload,
                timeout=25
            )
            
            if response.status_code == 200:
                recovery_successful = True
                print("   服务成功从错误中恢复")
            else:
                print(f"   恢复失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   恢复测试异常: {str(e)}")
        
        duration = time.time() - start_time
        
        message = "服务能够从错误中恢复" if recovery_successful else "服务无法从错误中恢复"
        self.log_test_result(test_name, recovery_successful, message, duration)
        return recovery_successful
    
    def run_smoke_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("=" * 80)
        print("🧪 GAC Agent Traveler 合并冒烟测试")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 执行测试序列
        tests = [
            self.test_service_connectivity,
            self.test_health_endpoint,
            self.test_ai_traveler_basic_request,
            self.test_stream_response,
            self.test_different_engines,
            self.test_parameter_boundaries,
            self.test_response_time_performance,
            self.test_error_recovery
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)
        
        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests
        
        print("📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if overall_success:
            print("🎉 冒烟测试全部通过！服务功能正常")
        elif passed_tests >= total_tests * 0.75:
            print("✅ 大部分冒烟测试通过，服务基本正常")
        else:
            print("⚠️  多项冒烟测试失败，请检查服务状态")
        
        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    smoke_test = CombinedSmokeTest(base_url)
    success = smoke_test.run_smoke_tests()
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
