#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冒烟测试 03 - GAC Agent Traveler 性能和稳定性测试
测试服务地址: http://127.0.0.1:8080

冒烟测试目标：
1. 验证响应时间性能
2. 验证内存泄漏检测
3. 验证长时间运行稳定性
4. 验证错误恢复能力
"""

import requests
import time
import json
import sys
import psutil
import threading
from typing import List, Dict, Any, Tuple
from statistics import mean, median


class SmokeTest03:
    """GAC Agent Traveler 性能和稳定性测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/gac-agent-traveler/v1/health"
        self.ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, duration: float = 0):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name} | {message} | {duration:.2f}s")
    
    def test_response_time_performance(self) -> bool:
        """测试1: 响应时间性能测试"""
        test_name = "响应时间性能测试"
        start_time = time.time()
        
        payload = {
            "query": "成都有什么特色景点？",
            "stream": False,
            "k": 5,
            "engine": "tencent",
            "detect": False,
            "location": {
                "lat": "30.5728",
                "lon": "104.0668"
            },
            "user_info": {
                "car_id": "perf_test_car",
                "user_id": "perf_test_user",
                "category": ["旅游"]
            }
        }
        
        response_times = []
        successful_requests = 0
        test_rounds = 10
        
        print(f"   执行 {test_rounds} 轮性能测试...")
        
        for i in range(test_rounds):
            try:
                req_start = time.time()
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                req_duration = time.time() - req_start
                
                if response.status_code == 200:
                    response_times.append(req_duration)
                    successful_requests += 1
                    print(f"   第{i+1}轮: {req_duration:.2f}s")
                else:
                    print(f"   第{i+1}轮: 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"   第{i+1}轮: 异常 - {str(e)}")
        
        duration = time.time() - start_time
        
        if response_times:
            avg_time = mean(response_times)
            median_time = median(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            # 性能标准：平均响应时间 < 10秒，成功率 > 80%
            success_rate = successful_requests / test_rounds
            performance_ok = avg_time < 10.0 and success_rate > 0.8
            
            message = f"平均:{avg_time:.2f}s 中位数:{median_time:.2f}s 最大:{max_time:.2f}s 最小:{min_time:.2f}s 成功率:{success_rate:.1%}"
            self.log_test_result(test_name, performance_ok, message, duration)
            return performance_ok
        else:
            self.log_test_result(test_name, False, "所有请求都失败", duration)
            return False
    
    def test_memory_usage_monitoring(self) -> bool:
        """测试2: 内存使用监控测试"""
        test_name = "内存使用监控测试"
        start_time = time.time()
        
        # 获取当前进程的内存使用情况（作为基准）
        current_process = psutil.Process()
        initial_memory = current_process.memory_info().rss / 1024 / 1024  # MB
        
        payload = {
            "query": "西安有什么历史文化景点？",
            "stream": False,
            "k": 5,
            "engine": "tencent",
            "detect": False,
            "location": {
                "lat": "34.3416",
                "lon": "108.9398"
            },
            "user_info": {
                "car_id": "memory_test_car",
                "user_id": "memory_test_user",
                "category": ["文化", "历史"]
            }
        }
        
        memory_readings = [initial_memory]
        successful_requests = 0
        test_rounds = 5
        
        print(f"   初始内存使用: {initial_memory:.1f}MB")
        
        for i in range(test_rounds):
            try:
                response = requests.post(
                    self.ai_traveler_url,
                    headers=self.headers,
                    json=payload,
                    timeout=25
                )
                
                if response.status_code == 200:
                    successful_requests += 1
                
                # 记录内存使用
                current_memory = current_process.memory_info().rss / 1024 / 1024
                memory_readings.append(current_memory)
                print(f"   第{i+1}轮后内存: {current_memory:.1f}MB")
                
                # 短暂休息，让系统稳定
                time.sleep(1)
                
            except Exception as e:
                print(f"   第{i+1}轮异常: {str(e)}")
        
        duration = time.time() - start_time
        final_memory = memory_readings[-1]
        memory_increase = final_memory - initial_memory
        
        # 内存标准：增长不超过50MB，或增长率不超过50%
        memory_ok = memory_increase < 50 or (memory_increase / initial_memory) < 0.5
        
        message = f"初始:{initial_memory:.1f}MB 最终:{final_memory:.1f}MB 增长:{memory_increase:.1f}MB"
        self.log_test_result(test_name, memory_ok, message, duration)
        return memory_ok
    
    def test_stability_under_load(self) -> bool:
        """测试3: 负载下稳定性测试"""
        test_name = "负载下稳定性测试"
        start_time = time.time()
        
        def make_continuous_requests(thread_id: int, results: List[bool]):
            """持续发送请求的线程函数"""
            payload = {
                "query": f"重庆有什么美食推荐？线程{thread_id}",
                "stream": False,
                "k": 3,
                "engine": "tencent",
                "detect": False,
                "location": {
                    "lat": "29.5647",
                    "lon": "106.5507"
                },
                "user_info": {
                    "car_id": f"stability_test_car_{thread_id}",
                    "user_id": f"stability_test_user_{thread_id}",
                    "category": ["美食"]
                }
            }
            
            thread_success_count = 0
            thread_total_count = 0
            
            # 每个线程运行30秒
            end_time = time.time() + 30
            
            while time.time() < end_time:
                try:
                    response = requests.post(
                        self.ai_traveler_url,
                        headers=self.headers,
                        json=payload,
                        timeout=15
                    )
                    
                    thread_total_count += 1
                    if response.status_code == 200:
                        thread_success_count += 1
                    
                    # 短暂休息
                    time.sleep(2)
                    
                except Exception:
                    thread_total_count += 1
            
            # 计算线程成功率
            thread_success_rate = thread_success_count / thread_total_count if thread_total_count > 0 else 0
            results.append(thread_success_rate > 0.7)  # 70%成功率阈值
            
            print(f"   线程{thread_id}: {thread_success_count}/{thread_total_count} 成功率:{thread_success_rate:.1%}")
        
        # 启动3个并发线程
        thread_count = 3
        thread_results = []
        threads = []
        
        print(f"   启动 {thread_count} 个并发线程，每个运行30秒...")
        
        for i in range(thread_count):
            thread = threading.Thread(target=make_continuous_requests, args=(i, thread_results))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        duration = time.time() - start_time
        
        # 稳定性标准：至少2/3的线程成功率达标
        successful_threads = sum(thread_results)
        stability_ok = successful_threads >= (thread_count * 2 // 3)
        
        message = f"成功线程: {successful_threads}/{thread_count}"
        self.log_test_result(test_name, stability_ok, message, duration)
        return stability_ok
    
    def test_error_recovery(self) -> bool:
        """测试4: 错误恢复能力测试"""
        test_name = "错误恢复能力测试"
        start_time = time.time()
        
        # 先发送一个错误请求
        invalid_payload = {
            "query": "",  # 空查询
            "invalid_field": "test"
        }
        
        try:
            # 发送无效请求
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=invalid_payload,
                timeout=10
            )
            print(f"   无效请求响应: {response.status_code}")
        except Exception as e:
            print(f"   无效请求异常: {str(e)}")
        
        # 短暂等待
        time.sleep(2)
        
        # 然后发送正常请求，测试服务是否能恢复
        valid_payload = {
            "query": "天津有什么特色小吃？",
            "stream": False,
            "k": 3,
            "engine": "tencent",
            "detect": False,
            "location": {
                "lat": "39.3434",
                "lon": "117.3616"
            },
            "user_info": {
                "car_id": "recovery_test_car",
                "user_id": "recovery_test_user",
                "category": ["美食"]
            }
        }
        
        recovery_successful = False
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=valid_payload,
                timeout=20
            )
            
            if response.status_code == 200:
                recovery_successful = True
                print("   服务成功从错误中恢复")
            else:
                print(f"   恢复失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   恢复测试异常: {str(e)}")
        
        duration = time.time() - start_time
        
        message = "服务能够从错误中恢复" if recovery_successful else "服务无法从错误中恢复"
        self.log_test_result(test_name, recovery_successful, message, duration)
        return recovery_successful
    
    def run_smoke_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("=" * 80)
        print("🧪 GAC Agent Traveler 冒烟测试 03 开始")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 执行测试序列
        tests = [
            self.test_response_time_performance,
            self.test_memory_usage_monitoring,
            self.test_stability_under_load,
            self.test_error_recovery
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_func in tests:
            if test_func():
                passed_tests += 1
            print("-" * 80)
        
        # 输出测试总结
        success_rate = (passed_tests / total_tests) * 100
        overall_success = passed_tests == total_tests
        
        print("📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if overall_success:
            print("🎉 冒烟测试全部通过！服务性能和稳定性良好")
        else:
            print("⚠️  部分冒烟测试失败，请检查服务性能")
        
        print("=" * 80)
        return overall_success


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    smoke_test = SmokeTest03(base_url)
    success = smoke_test.run_smoke_tests()
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
