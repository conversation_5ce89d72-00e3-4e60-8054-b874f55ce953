# GAC Agent Traveler 冒烟测试套件

本目录包含了 GAC Agent Traveler 服务的冒烟测试用例，用于验证服务的基本功能、性能和稳定性。

## 📋 测试概览

### 测试编号和功能

| 编号 | 测试文件 | 测试重点 | 预计耗时 |
|------|----------|----------|----------|
| 01 | `smoke_test_01.py` | 基础功能测试 | 1-2分钟 |
| 02 | `smoke_test_02.py` | 流式响应和边界测试 | 2-3分钟 |
| 03 | `smoke_test_03.py` | 性能和稳定性测试 | 3-5分钟 |

### 测试覆盖范围

#### 冒烟测试 01 - 基础功能测试
- ✅ 服务连通性验证
- ✅ 健康检查端点测试
- ✅ AI旅行家基础请求测试
- ✅ 无效请求处理测试

#### 冒烟测试 02 - 流式响应和边界测试
- ✅ 流式响应功能测试
- ✅ 不同搜索引擎配置测试
- ✅ 参数边界值测试
- ✅ 并发请求处理测试

#### 冒烟测试 03 - 性能和稳定性测试
- ✅ 响应时间性能测试
- ✅ 内存使用监控测试
- ✅ 负载下稳定性测试
- ✅ 错误恢复能力测试

## 🚀 快速开始

### 前置条件

1. **确保服务正在运行**
   ```bash
   # 启动服务
   cd gac-agent-traveler
   python main.py
   ```

2. **安装测试依赖**
   ```bash
   pip install requests psutil
   ```

### 运行单个测试

```bash
# 运行基础功能测试
python test/smoke_test_01.py

# 运行流式响应测试
python test/smoke_test_02.py

# 运行性能稳定性测试
python test/smoke_test_03.py

# 指定服务地址
python test/smoke_test_01.py http://127.0.0.1:8080
```

### 运行完整测试套件

```bash
# 运行所有冒烟测试
python test/run_smoke_tests.py

# 指定服务地址
python test/run_smoke_tests.py http://127.0.0.1:8080
```

## 📊 测试结果解读

### 成功标准

#### 基础功能测试 (01)
- 服务连通性：能够连接到服务
- 健康检查：返回状态 "ok"
- 基础请求：能够正常处理AI旅行家请求
- 错误处理：正确处理无效请求

#### 流式响应测试 (02)
- 流式响应：能够接收流式数据
- 搜索引擎：至少一个搜索引擎可用
- 边界测试：至少50%的边界情况通过
- 并发测试：至少50%的并发请求成功

#### 性能稳定性测试 (03)
- 响应时间：平均响应时间 < 10秒，成功率 > 80%
- 内存使用：内存增长 < 50MB 或增长率 < 50%
- 稳定性：至少2/3的并发线程成功率达标
- 错误恢复：服务能从错误请求中恢复

### 输出格式

```
✅ PASS | 测试名称 | 测试结果描述 | 耗时
❌ FAIL | 测试名称 | 失败原因 | 耗时
```

### 测试报告

运行完整测试套件后，会生成两个文件：
- `smoke_test_report_YYYYMMDD_HHMMSS.txt` - 人类可读的测试报告
- `smoke_test_results_YYYYMMDD_HHMMSS.json` - 机器可读的详细结果

## 🔧 配置说明

### 服务地址配置

默认测试地址：`http://127.0.0.1:8080`

可以通过命令行参数修改：
```bash
python test/smoke_test_01.py http://your-server:port
```

### 测试参数调整

如需调整测试参数，可以修改各测试文件中的配置：

```python
# smoke_test_03.py 中的性能测试参数
test_rounds = 10          # 性能测试轮数
test_duration = 30        # 稳定性测试持续时间(秒)
thread_count = 3          # 并发线程数
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ FAIL | 服务连通性测试 | 无法连接到服务
   ```
   - 检查服务是否启动
   - 检查端口是否正确
   - 检查防火墙设置

2. **请求超时**
   ```
   ❌ FAIL | AI旅行家基础请求测试 | 请求超时
   ```
   - 检查服务负载
   - 检查网络连接
   - 增加超时时间

3. **内存使用过高**
   ```
   ❌ FAIL | 内存使用监控测试 | 内存增长过多
   ```
   - 检查是否存在内存泄漏
   - 重启服务
   - 检查系统资源

### 调试模式

在测试文件中添加调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 持续集成

### CI/CD 集成示例

```yaml
# .github/workflows/smoke-tests.yml
name: Smoke Tests
on: [push, pull_request]

jobs:
  smoke-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install requests psutil
      - name: Start service
        run: |
          python main.py &
          sleep 10
      - name: Run smoke tests
        run: python test/run_smoke_tests.py
```

## 📝 扩展测试

### 添加新的测试用例

1. 创建新的测试文件：`smoke_test_04.py`
2. 继承测试模式：
   ```python
   class SmokeTest04:
       def __init__(self, base_url: str = "http://127.0.0.1:8080"):
           # 初始化代码
       
       def test_new_feature(self) -> bool:
           # 测试逻辑
           pass
       
       def run_smoke_tests(self) -> bool:
           # 运行测试
           pass
   ```
3. 测试运行器会自动发现新的测试文件

### 自定义测试场景

可以根据具体需求修改测试用例：
- 调整测试数据
- 修改性能阈值
- 增加特定业务场景测试

## 📞 支持

如有问题或建议，请联系开发团队或提交 Issue。
