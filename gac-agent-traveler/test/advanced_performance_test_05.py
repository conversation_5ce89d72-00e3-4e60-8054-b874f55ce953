#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent Traveler 高级性能分析测试 05
详细分析各个模块的性能表现和优化建议

测试内容：
1. 多轮性能测试
2. 模块耗时趋势分析
3. 性能瓶颈识别
4. 优化建议生成
5. 性能报告导出
"""

import requests
import time
import json
import sys
from typing import Dict, List, Any, Tuple
from statistics import mean, median, stdev
from datetime import datetime


class AdvancedPerformanceTest:
    """高级性能分析测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
        self.headers = {"Content-Type": "application/json"}
        self.test_results = []
        
        # 模块名称映射
        self.module_names = {
            "t1-loc": "位置解析",
            "t2-mem": "用户画像",
            "t3-intent": "意图识别", 
            "t4-src": "搜索执行",
            "t5-fetch": "内容爬取",
            "t6-mft": "内容总结",
            "t7-sft": "结构化处理",
            "TTFT": "首字符时间",
            "total_time": "总响应时间"
        }
    
    def extract_timing_data(self, response_data: Dict[str, Any]) -> Dict[str, float]:
        """提取时间数据"""
        timing_data = {}
        
        if isinstance(response_data, dict) and "time_cost" in response_data:
            time_cost = response_data["time_cost"]
            if isinstance(time_cost, dict):
                for key, value in time_cost.items():
                    try:
                        timing_data[key] = float(value)
                    except (ValueError, TypeError):
                        pass
        
        return timing_data
    
    def calculate_module_durations(self, timing_data: Dict[str, float]) -> Dict[str, float]:
        """计算各模块的实际耗时"""
        durations = {}
        
        # 直接时间点
        for key in ["t1-loc", "TTFT", "total_time"]:
            if key in timing_data:
                durations[key] = timing_data[key]
        
        # 计算增量时间
        time_points = ["t1-loc", "t2-mem", "t3-intent", "t4-src", "t5-fetch", "t6-mft", "t7-sft"]
        
        for i in range(1, len(time_points)):
            current_key = time_points[i]
            previous_key = time_points[i-1]
            
            if current_key in timing_data and previous_key in timing_data:
                duration = timing_data[current_key] - timing_data[previous_key]
                durations[current_key] = duration
        
        return durations
    
    def perform_single_test(self, query: str, test_id: int) -> Dict[str, Any]:
        """执行单次性能测试"""
        payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": query,
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference", 
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            total_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                timing_data = self.extract_timing_data(result)
                module_durations = self.calculate_module_durations(timing_data)
                
                test_result = {
                    "test_id": test_id,
                    "query": query,
                    "timestamp": datetime.now().isoformat(),
                    "total_time": total_time,
                    "server_total_time": timing_data.get("total_time", 0),
                    "timing_data": timing_data,
                    "module_durations": module_durations,
                    "success": True
                }
                
                return test_result
            else:
                return {
                    "test_id": test_id,
                    "query": query,
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            return {
                "test_id": test_id,
                "query": query,
                "success": False,
                "error": str(e)
            }
    
    def run_performance_tests(self, test_rounds: int = 10) -> None:
        """运行多轮性能测试"""
        print(f"🚀 开始执行 {test_rounds} 轮性能测试...")
        
        test_queries = [
            "上海有什么特色美食？",
            "北京有哪些著名景点？", 
            "深圳购物中心推荐",
            "杭州周边有什么好玩的？",
            "成都特色小吃有哪些？",
            "广州早茶哪里好？",
            "西安历史景点推荐",
            "苏州园林介绍"
        ]
        
        for round_num in range(test_rounds):
            query = test_queries[round_num % len(test_queries)]
            print(f"   第 {round_num + 1:2d} 轮: {query}")
            
            result = self.perform_single_test(query, round_num + 1)
            self.test_results.append(result)
            
            if result["success"]:
                total_time = result["total_time"]
                server_time = result.get("server_total_time", 0)
                print(f"        ✅ 完成 - 客户端: {total_time:.2f}s, 服务端: {server_time:.2f}s")
            else:
                print(f"        ❌ 失败 - {result.get('error', '未知错误')}")
            
            # 避免请求过于频繁
            if round_num < test_rounds - 1:
                time.sleep(0.5)
    
    def analyze_performance_statistics(self) -> None:
        """分析性能统计数据"""
        successful_tests = [t for t in self.test_results if t.get("success", False)]
        
        if not successful_tests:
            print("❌ 没有成功的测试数据可供分析")
            return
        
        print("\n" + "=" * 80)
        print("📊 性能统计分析")
        print("=" * 80)
        
        # 总体统计
        total_times = [t["total_time"] for t in successful_tests]
        server_times = [t.get("server_total_time", 0) for t in successful_tests]
        
        print(f"📈 总体响应时间统计 (基于 {len(successful_tests)} 次成功测试):")
        print(f"   客户端时间 - 平均: {mean(total_times):.2f}s, 中位数: {median(total_times):.2f}s")
        print(f"   服务端时间 - 平均: {mean(server_times):.2f}s, 中位数: {median(server_times):.2f}s")
        print(f"   最快响应: {min(total_times):.2f}s, 最慢响应: {max(total_times):.2f}s")
        if len(total_times) > 1:
            print(f"   标准差: {stdev(total_times):.2f}s")
        
        # 模块耗时统计
        print(f"\n🔧 各模块平均耗时分析:")
        module_stats = {}
        
        for test in successful_tests:
            for module_key, duration in test.get("module_durations", {}).items():
                if module_key not in module_stats:
                    module_stats[module_key] = []
                module_stats[module_key].append(duration)
        
        # 按平均耗时排序
        sorted_modules = sorted(module_stats.items(), 
                              key=lambda x: mean(x[1]), reverse=True)
        
        for module_key, durations in sorted_modules:
            module_name = self.module_names.get(module_key, module_key)
            avg_duration = mean(durations)
            max_duration = max(durations)
            min_duration = min(durations)
            
            print(f"   {module_name:12s}: 平均 {avg_duration:6.2f}s, "
                  f"最大 {max_duration:6.2f}s, 最小 {min_duration:6.2f}s")
    
    def identify_performance_bottlenecks(self) -> None:
        """识别性能瓶颈"""
        successful_tests = [t for t in self.test_results if t.get("success", False)]
        
        if not successful_tests:
            return
        
        print("\n" + "=" * 80)
        print("🔍 性能瓶颈分析")
        print("=" * 80)
        
        # 计算各模块的平均耗时占比
        module_percentages = {}
        
        for test in successful_tests:
            total_time = test.get("server_total_time", test["total_time"])
            for module_key, duration in test.get("module_durations", {}).items():
                if module_key not in module_percentages:
                    module_percentages[module_key] = []
                
                percentage = (duration / total_time * 100) if total_time > 0 else 0
                module_percentages[module_key].append(percentage)
        
        # 找出耗时最多的模块
        avg_percentages = {k: mean(v) for k, v in module_percentages.items()}
        sorted_percentages = sorted(avg_percentages.items(), 
                                  key=lambda x: x[1], reverse=True)
        
        print("⚠️  主要性能瓶颈 (按平均耗时占比排序):")
        for module_key, avg_percentage in sorted_percentages[:5]:
            module_name = self.module_names.get(module_key, module_key)
            print(f"   {module_name:12s}: {avg_percentage:5.1f}% 的总时间")
        
        # 生成优化建议
        print(f"\n💡 优化建议:")
        top_bottleneck = sorted_percentages[0] if sorted_percentages else None
        
        if top_bottleneck:
            module_key, percentage = top_bottleneck
            module_name = self.module_names.get(module_key, module_key)
            
            suggestions = {
                "首字符时间": "考虑优化模型推理速度或使用更快的硬件",
                "搜索执行": "优化搜索算法或使用缓存机制",
                "内容爬取": "并行爬取或使用更快的网络连接",
                "内容总结": "优化文本处理算法或使用更高效的模型",
                "用户画像": "缓存用户数据或优化数据库查询",
                "位置解析": "使用地理编码缓存或更快的地理服务"
            }
            
            suggestion = suggestions.get(module_name, "分析具体实现细节，寻找优化空间")
            print(f"   1. 重点优化 '{module_name}' 模块 (占用 {percentage:.1f}% 时间)")
            print(f"      建议: {suggestion}")
    
    def generate_performance_report(self) -> None:
        """生成性能报告"""
        successful_tests = [t for t in self.test_results if t.get("success", False)]
        failed_tests = [t for t in self.test_results if not t.get("success", False)]
        
        print("\n" + "=" * 80)
        print("📋 性能测试报告")
        print("=" * 80)
        
        print(f"🎯 测试概况:")
        print(f"   总测试次数: {len(self.test_results)}")
        print(f"   成功次数: {len(successful_tests)}")
        print(f"   失败次数: {len(failed_tests)}")
        print(f"   成功率: {len(successful_tests)/len(self.test_results)*100:.1f}%")
        
        if successful_tests:
            total_times = [t["total_time"] for t in successful_tests]
            print(f"\n⏱️  响应时间表现:")
            print(f"   平均响应时间: {mean(total_times):.2f}s")
            print(f"   95%分位数: {sorted(total_times)[int(len(total_times)*0.95)]:.2f}s")
            print(f"   最佳响应时间: {min(total_times):.2f}s")
            print(f"   最差响应时间: {max(total_times):.2f}s")
        
        if failed_tests:
            print(f"\n❌ 失败测试分析:")
            error_counts = {}
            for test in failed_tests:
                error = test.get("error", "未知错误")
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in error_counts.items():
                print(f"   {error}: {count} 次")
    
    def run_advanced_performance_test(self, test_rounds: int = 10) -> None:
        """运行高级性能测试"""
        print("=" * 80)
        print("🧪 GAC Agent Traveler 高级性能分析测试 05")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔄 测试轮数: {test_rounds}")
        print("=" * 80)
        
        # 执行性能测试
        self.run_performance_tests(test_rounds)
        
        # 分析结果
        self.analyze_performance_statistics()
        self.identify_performance_bottlenecks()
        self.generate_performance_report()
        
        print("\n" + "=" * 80)
        print("✅ 高级性能分析测试完成")
        print("=" * 80)


def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    test_rounds = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    
    test = AdvancedPerformanceTest(base_url)
    test.run_advanced_performance_test(test_rounds)


if __name__ == "__main__":
    main()
