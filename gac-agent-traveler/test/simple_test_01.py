#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent Traveler 简化测试程序 01
测试基本功能和连通性
"""

import requests
import time
import json
import sys

def test_service_health():
    """测试服务健康状态"""
    print("=" * 60)
    print("🧪 GAC Agent Traveler 简化测试 01")
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8080"
    health_url = f"{base_url}/gac-agent-traveler/v1/health"
    
    print(f"🎯 测试目标: {base_url}")
    print(f"🔍 健康检查: {health_url}")
    
    try:
        print("\n1. 测试服务连通性...")
        response = requests.get(base_url, timeout=10)
        print(f"   基础连接状态码: {response.status_code}")
        
        if response.status_code in [200, 404]:
            print("   ✅ 服务连通正常")
        else:
            print("   ❌ 服务连通异常")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 无法连接到服务")
        print("   💡 请确保服务已启动: python main.py")
        return False
    except Exception as e:
        print(f"   ❌ 连接异常: {str(e)}")
        return False
    
    try:
        print("\n2. 测试健康检查端点...")
        response = requests.get(health_url, timeout=10)
        print(f"   健康检查状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                health_data = response.json()
                print(f"   健康检查响应: {health_data}")
                
                if health_data.get("status") == "ok":
                    version = health_data.get("version", "未知")
                    print(f"   ✅ 健康检查通过 (版本: {version})")
                    return True
                else:
                    print(f"   ❌ 健康状态异常: {health_data}")
                    return False
            except json.JSONDecodeError:
                print("   ❌ 健康检查响应格式错误")
                return False
        else:
            print(f"   ❌ 健康检查失败 (状态码: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"   ❌ 健康检查异常: {str(e)}")
        return False

def test_basic_ai_request():
    """测试基本AI请求"""
    print("\n3. 测试基本AI请求...")
    
    ai_url = "http://127.0.0.1:8080/gac-agent-traveler/v1/ai_traveler"
    
    payload = {
        "detect": False,
        "engine": "tencent",
        "location": {
            "lat": "31.16813",
            "lon": "121.3999"
        },
        "query": "今天吃什么",
        "stream": False,
        "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
        "use_search_cache": False,
        "user_info": {
            "car_id": "demoCar82952",
            "category": [
                "natural_landscape_preference",
                "human_landscape_preference",
                "entertainment_landscape_preference",
                "travel_activity"
            ],
            "user_id": "1"
        }
    }
    
    headers = {"Content-Type": "application/json"}
    
    try:
        print("   发送AI请求...")
        start_time = time.time()
        
        response = requests.post(
            ai_url,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        duration = time.time() - start_time
        print(f"   请求耗时: {duration:.2f}s")
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                response_length = len(str(result))
                print(f"   响应数据长度: {response_length}")
                print("   ✅ AI请求成功")
                
                # 尝试提取一些关键信息
                if isinstance(result, dict):
                    if "answer" in result:
                        answer_preview = str(result["answer"])[:100] + "..." if len(str(result["answer"])) > 100 else str(result["answer"])
                        print(f"   回答预览: {answer_preview}")
                    
                return True
            except json.JSONDecodeError:
                print("   ❌ 响应JSON格式错误")
                return False
        else:
            error_msg = response.text[:200] if response.text else "无错误信息"
            print(f"   ❌ AI请求失败: {error_msg}")
            return False
            
    except requests.exceptions.Timeout:
        print("   ❌ 请求超时")
        return False
    except Exception as e:
        print(f"   ❌ 请求异常: {str(e)}")
        return False

def main():
    """主函数"""
    # 测试服务健康状态
    health_ok = test_service_health()
    
    if not health_ok:
        print("\n❌ 健康检查失败，跳过后续测试")
        print("=" * 60)
        sys.exit(1)
    
    # 测试基本AI请求
    ai_ok = test_basic_ai_request()
    
    print("\n📊 测试总结:")
    print(f"   健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"   AI请求: {'✅ 通过' if ai_ok else '❌ 失败'}")
    
    if health_ok and ai_ok:
        print("🎉 所有测试通过！服务功能正常")
        print("=" * 60)
        sys.exit(0)
    else:
        print("⚠️  部分测试失败，请检查服务状态")
        print("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    main()
