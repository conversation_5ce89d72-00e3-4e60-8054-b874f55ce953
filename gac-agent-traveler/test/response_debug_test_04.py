#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent Traveler 响应数据调试测试 04
用于调试和分析响应数据的结构，找出时间字段的位置

测试内容：
1. 发送单次请求
2. 完整打印响应数据结构
3. 查找所有可能的时间字段
4. 分析数据格式
"""

import requests
import time
import json
import sys
from typing import Dict, Any


class ResponseDebugTest:
    """响应数据调试测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
        self.headers = {"Content-Type": "application/json"}
    
    def find_time_fields(self, data: Any, path: str = "") -> Dict[str, Any]:
        """递归查找所有可能的时间字段"""
        time_fields = {}
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                # 检查是否是时间相关的字段
                if any(time_keyword in key.lower() for time_keyword in 
                       ['time', 't1', 't2', 't3', 't4', 't5', 't6', 't7', 't8', 't9', 't10', 'ttft', 'duration']):
                    time_fields[current_path] = value
                
                # 递归查找嵌套结构
                if isinstance(value, (dict, list)):
                    nested_fields = self.find_time_fields(value, current_path)
                    time_fields.update(nested_fields)
        
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                if isinstance(item, (dict, list)):
                    nested_fields = self.find_time_fields(item, current_path)
                    time_fields.update(nested_fields)
        
        return time_fields
    
    def analyze_response_structure(self, data: Any, indent: int = 0) -> str:
        """分析响应数据结构"""
        lines = []
        prefix = "  " * indent
        
        if isinstance(data, dict):
            lines.append(f"{prefix}Dict ({len(data)} keys):")
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    lines.append(f"{prefix}  {key}: {type(value).__name__}")
                    if len(lines) < 50:  # 限制输出长度
                        lines.extend(self.analyze_response_structure(value, indent + 2).split('\n'))
                else:
                    value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    lines.append(f"{prefix}  {key}: {type(value).__name__} = {value_str}")
        
        elif isinstance(data, list):
            lines.append(f"{prefix}List ({len(data)} items):")
            for i, item in enumerate(data[:3]):  # 只显示前3个元素
                lines.append(f"{prefix}  [{i}]: {type(item).__name__}")
                if isinstance(item, (dict, list)) and len(lines) < 50:
                    lines.extend(self.analyze_response_structure(item, indent + 2).split('\n'))
            if len(data) > 3:
                lines.append(f"{prefix}  ... ({len(data) - 3} more items)")
        
        else:
            value_str = str(data)[:100] + "..." if len(str(data)) > 100 else str(data)
            lines.append(f"{prefix}{type(data).__name__}: {value_str}")
        
        return '\n'.join(lines)
    
    def debug_single_request(self) -> None:
        """调试单次请求的响应数据"""
        print("🔍 发送调试请求...")
        
        payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": "上海有什么特色小吃？",
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            total_time = time.time() - start_time
            print(f"✅ 请求完成，总耗时: {total_time:.2f}s")
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    print("\n" + "=" * 60)
                    print("📋 响应数据结构分析:")
                    print("=" * 60)
                    structure = self.analyze_response_structure(result)
                    print(structure)
                    
                    print("\n" + "=" * 60)
                    print("⏰ 时间字段查找:")
                    print("=" * 60)
                    time_fields = self.find_time_fields(result)
                    
                    if time_fields:
                        for path, value in time_fields.items():
                            print(f"   {path}: {value} ({type(value).__name__})")
                    else:
                        print("   ⚠️  未找到时间相关字段")
                    
                    print("\n" + "=" * 60)
                    print("📄 完整响应数据 (JSON格式):")
                    print("=" * 60)
                    print(json.dumps(result, indent=2, ensure_ascii=False)[:2000] + "...")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"📄 原始响应内容: {response.text[:500]}...")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"📄 错误内容: {response.text[:500]}...")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    def run_debug_test(self) -> None:
        """运行调试测试"""
        print("=" * 80)
        print("🧪 GAC Agent Traveler 响应数据调试测试 04")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        self.debug_single_request()
        
        print("\n" + "=" * 80)
        print("✅ 调试测试完成")
        print("=" * 80)


def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    test = ResponseDebugTest(base_url)
    test.run_debug_test()


if __name__ == "__main__":
    main()
