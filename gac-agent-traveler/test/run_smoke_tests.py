#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冒烟测试运行器 - GAC Agent Traveler
批量运行所有冒烟测试并生成测试报告

使用方法:
python run_smoke_tests.py [服务地址]

示例:
python run_smoke_tests.py http://127.0.0.1:8080
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Any


class SmokeTestRunner:
    """冒烟测试运行器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.test_dir = Path(__file__).parent
        self.results = []
        
    def find_smoke_tests(self) -> List[Path]:
        """查找所有冒烟测试文件"""
        test_files = []
        for file_path in self.test_dir.glob("smoke_test_*.py"):
            if file_path.name != "run_smoke_tests.py":
                test_files.append(file_path)
        
        # 按文件名排序，确保按编号顺序执行
        test_files.sort(key=lambda x: x.name)
        return test_files
    
    def run_single_test(self, test_file: Path) -> Dict[str, Any]:
        """运行单个测试文件"""
        test_name = test_file.stem
        print(f"\n🚀 运行测试: {test_name}")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # 运行测试脚本
            result = subprocess.run(
                [sys.executable, str(test_file), self.base_url],
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            duration = time.time() - start_time
            success = result.returncode == 0
            
            # 打印测试输出
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print("错误输出:", result.stderr)
            
            return {
                "test_name": test_name,
                "file_path": str(test_file),
                "success": success,
                "duration": duration,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            print(f"❌ 测试超时: {test_name}")
            return {
                "test_name": test_name,
                "file_path": str(test_file),
                "success": False,
                "duration": duration,
                "return_code": -1,
                "stdout": "",
                "stderr": "测试执行超时",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ 测试执行异常: {test_name} - {str(e)}")
            return {
                "test_name": test_name,
                "file_path": str(test_file),
                "success": False,
                "duration": duration,
                "return_code": -1,
                "stdout": "",
                "stderr": f"执行异常: {str(e)}",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def generate_report(self) -> str:
        """生成测试报告"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["success"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        total_duration = sum(r["duration"] for r in self.results)
        
        report = []
        report.append("=" * 80)
        report.append("📋 GAC Agent Traveler 冒烟测试报告")
        report.append("=" * 80)
        report.append(f"🎯 测试目标: {self.base_url}")
        report.append(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"📊 测试统计:")
        report.append(f"   总测试数: {total_tests}")
        report.append(f"   通过测试: {passed_tests}")
        report.append(f"   失败测试: {failed_tests}")
        report.append(f"   成功率: {success_rate:.1f}%")
        report.append(f"   总耗时: {total_duration:.2f}秒")
        report.append("")
        
        # 详细测试结果
        report.append("📝 详细测试结果:")
        report.append("-" * 80)
        
        for result in self.results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            report.append(f"{status} | {result['test_name']} | {result['duration']:.2f}s")
            
            if not result["success"] and result["stderr"]:
                report.append(f"     错误: {result['stderr']}")
        
        report.append("")
        
        # 总结
        if passed_tests == total_tests:
            report.append("🎉 所有冒烟测试通过！服务功能正常")
        elif passed_tests > 0:
            report.append("⚠️  部分冒烟测试失败，请检查服务状态")
        else:
            report.append("🚨 所有冒烟测试失败，服务可能存在严重问题")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_report(self, report: str) -> str:
        """保存测试报告到文件"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = self.test_dir / f"smoke_test_report_{timestamp}.txt"
        
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report)
        
        # 同时保存JSON格式的详细结果
        json_file = self.test_dir / f"smoke_test_results_{timestamp}.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        return str(report_file)
    
    def run_all_tests(self) -> bool:
        """运行所有冒烟测试"""
        print("🧪 GAC Agent Traveler 冒烟测试套件")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 查找所有测试文件
        test_files = self.find_smoke_tests()
        
        if not test_files:
            print("❌ 未找到任何冒烟测试文件")
            return False
        
        print(f"📁 找到 {len(test_files)} 个测试文件:")
        for test_file in test_files:
            print(f"   - {test_file.name}")
        
        # 运行所有测试
        for test_file in test_files:
            result = self.run_single_test(test_file)
            self.results.append(result)
        
        # 生成并显示报告
        report = self.generate_report()
        print("\n" + report)
        
        # 保存报告
        report_file = self.save_report(report)
        print(f"\n📄 测试报告已保存到: {report_file}")
        
        # 返回整体测试结果
        return all(r["success"] for r in self.results)


def main():
    """主函数"""
    # 可以通过命令行参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    runner = SmokeTestRunner(base_url)
    success = runner.run_all_tests()
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
