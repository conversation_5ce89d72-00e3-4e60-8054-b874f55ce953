#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间信息提取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smoke_test_combined import CombinedSmokeTest

def test_timing_extraction():
    """测试时间信息提取功能"""
    smoke_test = CombinedSmokeTest()
    
    # 模拟响应数据
    mock_response = {
        "t1-loc": 0.1,
        "t2-mem": 0.3,
        "t3-pre-intent": 0.5,
        "t3-intent": 0.8,
        "t4-src": 2.5,
        "t5-fetch": 3.2,
        "t6-mft": 4.0,
        "t7-sft": 4.1,
        "t8-list": 4.5,
        "t9-brief": 5.0,
        "t10-detail": 6.0,
        "t-acft": 0.2,
        "TTFT": 0.15
    }
    
    print("测试时间信息提取功能...")
    timing_info = smoke_test.extract_timing_info(mock_response)
    print(f"提取的时间信息: {timing_info}")
    
    timing_summary = smoke_test.format_timing_summary(timing_info)
    print(f"格式化的时间摘要: {timing_summary}")
    
    # 测试嵌套的时间信息
    nested_response = {
        "data": "some data",
        "time_info": {
            "t1-loc": 0.1,
            "t2-mem": 0.3,
            "t3-intent": 0.8,
            "t4-src": 2.5,
            "t5-fetch": 3.2,
            "t6-mft": 4.0
        }
    }
    
    print("\n测试嵌套时间信息提取...")
    nested_timing_info = smoke_test.extract_timing_info(nested_response)
    print(f"嵌套提取的时间信息: {nested_timing_info}")
    
    nested_timing_summary = smoke_test.format_timing_summary(nested_timing_info)
    print(f"嵌套格式化的时间摘要: {nested_timing_summary}")

if __name__ == "__main__":
    test_timing_extraction()
