#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速冒烟测试 - GAC Agent Traveler
用于快速验证服务基本功能是否正常

使用方法:
python quick_smoke_test.py [服务地址]

示例:
python quick_smoke_test.py http://127.0.0.1:8080
"""

import requests
import time
import json
import sys


def quick_health_check(base_url: str) -> bool:
    """快速健康检查"""
    health_url = f"{base_url}/gac-agent-traveler/v1/health"
    
    try:
        print("🏥 检查服务健康状态...")
        response = requests.get(health_url, timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            if health_data.get("status") == "ok":
                version = health_data.get("version", "未知")
                print(f"✅ 服务健康 (版本: {version})")
                return True
            else:
                print(f"❌ 服务状态异常: {health_data}")
                return False
        else:
            print(f"❌ 健康检查失败 (状态码: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False


def quick_api_test(base_url: str) -> bool:
    """快速API功能测试"""
    ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
    headers = {"Content-Type": "application/json"}
    
    payload = {
        "query": "北京有什么好玩的地方？",
        "stream": False,
        "k": 3,
        "engine": "tencent",
        "detect": False,
        "location": {
            "lat": "39.9042",
            "lon": "116.4074"
        },
        "user_info": {
            "car_id": "quick_test_car",
            "user_id": "quick_test_user",
            "category": ["旅游"]
        }
    }
    
    try:
        print("🚀 测试AI旅行家API...")
        start_time = time.time()
        
        response = requests.post(
            ai_traveler_url,
            headers=headers,
            json=payload,
            timeout=20
        )
        
        duration = time.time() - start_time
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ API测试成功 (耗时: {duration:.2f}s)")
                print(f"📝 响应长度: {len(str(result))} 字符")
                return True
            except json.JSONDecodeError:
                print(f"❌ 响应格式错误 (耗时: {duration:.2f}s)")
                return False
        else:
            print(f"❌ API测试失败 (状态码: {response.status_code}, 耗时: {duration:.2f}s)")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API请求超时")
        return False
    except Exception as e:
        print(f"❌ API测试异常: {str(e)}")
        return False


def main():
    """主函数"""
    # 获取服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    print("=" * 60)
    print("🧪 GAC Agent Traveler 快速冒烟测试")
    print(f"🎯 测试目标: {base_url}")
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 执行快速测试
    tests_passed = 0
    total_tests = 2
    
    # 1. 健康检查
    if quick_health_check(base_url):
        tests_passed += 1
    
    print("-" * 60)
    
    # 2. API功能测试
    if quick_api_test(base_url):
        tests_passed += 1
    
    print("-" * 60)
    
    # 输出结果
    success_rate = (tests_passed / total_tests) * 100
    
    print("📊 测试结果:")
    print(f"   通过测试: {tests_passed}/{total_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 快速冒烟测试通过！服务基本功能正常")
        print("💡 建议运行完整测试套件: python test/run_smoke_tests.py")
        sys.exit(0)
    else:
        print("⚠️  快速冒烟测试失败，请检查服务状态")
        print("🔍 运行详细测试获取更多信息: python test/run_smoke_tests.py")
        sys.exit(1)


if __name__ == "__main__":
    main()
