[{"test_name": "smoke_test_01", "file_path": "/data/lql/agents/gac-agent-traveler/test/smoke_test_01.py", "success": true, "duration": 15.658401012420654, "return_code": 0, "stdout": "================================================================================\n🧪 GAC Agent Traveler 冒烟测试 01 开始\n🎯 测试目标: http://127.0.0.1:8080\n⏰ 测试时间: 2025-09-02 16:04:49\n================================================================================\n✅ PASS | 服务连通性测试 | 服务连通正常 (状态码: 404) | 0.00s\n--------------------------------------------------------------------------------\n✅ PASS | 健康检查端点测试 | 健康检查通过 (版本: 1.0.21) | 0.00s\n--------------------------------------------------------------------------------\n✅ PASS | AI旅行家基础请求测试 | AI旅行家请求成功 (响应长度: 250) | 15.54s\n--------------------------------------------------------------------------------\n✅ PASS | AI旅行家无效请求测试 | 正确处理无效请求 (状态码: 422) | 0.01s\n--------------------------------------------------------------------------------\n📊 测试总结:\n   总测试数: 4\n   通过测试: 4\n   失败测试: 0\n   成功率: 100.0%\n🎉 冒烟测试全部通过！服务基础功能正常\n================================================================================\n", "stderr": "", "timestamp": "2025-09-02 16:05:04"}, {"test_name": "smoke_test_02", "file_path": "/data/lql/agents/gac-agent-traveler/test/smoke_test_02.py", "success": false, "duration": 105.21282505989075, "return_code": 1, "stdout": "================================================================================\n🧪 GAC Agent Traveler 冒烟测试 02 开始\n🎯 测试目标: http://127.0.0.1:8080\n⏰ 测试时间: 2025-09-02 16:05:04\n================================================================================\n✅ PASS | 流式响应测试 | 流式响应正常 (接收71个数据块) | 15.48s\n--------------------------------------------------------------------------------\n   引擎 tencent 测试异常: HTTPConnectionPool(host='127.0.0.1', port=8080): Read timed out. (read timeout=15)\n   引擎 serper 测试异常: HTTPConnectionPool(host='127.0.0.1', port=8080): Read timed out. (read timeout=15)\n✅ PASS | 不同搜索引擎测试 | 成功的引擎: bing | 31.96s\n--------------------------------------------------------------------------------\n   ✓ 最小k值: 成功\n   ✓ 较大k值: 成功\n   ✓ 最短查询: 成功\n   ✓ 超长查询: 成功\n✅ PASS | 参数边界值测试 | 边界测试通过 (4/4) | 32.62s\n--------------------------------------------------------------------------------\n❌ FAIL | 并发请求测试 | 并发测试失败 (1/5) | 25.04s\n--------------------------------------------------------------------------------\n📊 测试总结:\n   总测试数: 4\n   通过测试: 3\n   失败测试: 1\n   成功率: 75.0%\n⚠️  部分冒烟测试失败，请检查服务配置\n================================================================================\n", "stderr": "", "timestamp": "2025-09-02 16:06:49"}, {"test_name": "smoke_test_03", "file_path": "/data/lql/agents/gac-agent-traveler/test/smoke_test_03.py", "success": false, "duration": 0.19762516021728516, "return_code": 1, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"/data/lql/agents/gac-agent-traveler/test/smoke_test_03.py\", line 18, in <module>\n    import psutil\nModuleNotFoundError: No module named 'psutil'\n", "timestamp": "2025-09-02 16:06:50"}]