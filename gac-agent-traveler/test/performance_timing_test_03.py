#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAC Agent Traveler 性能时间统计测试 03
专门测试各个模块的耗时统计功能

测试内容：
1. 单次请求的详细时间分析
2. 多次请求的时间统计对比
3. 不同查询类型的时间分析
4. 时间数据的可视化展示
"""

import requests
import time
import json
import sys
from typing import Dict, List, Any
from statistics import mean, median, stdev


class PerformanceTimingTest:
    """性能时间统计测试类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.ai_traveler_url = f"{base_url}/gac-agent-traveler/v1/ai_traveler"
        self.headers = {"Content-Type": "application/json"}
        self.timing_data = []
        
    def extract_detailed_timing(self, response_data: Dict[str, Any]) -> Dict[str, float]:
        """提取详细的时间信息"""
        timing_info = {}

        if isinstance(response_data, dict):
            # 所有可能的时间字段
            time_fields = [
                "t1-loc", "t2-mem", "t3-pre-intent", "t3-intent",
                "t4-src", "t5-fetch", "t6-mft", "t7-sft",
                "t8-list", "t9-brief", "t10-detail", "t-acft", "TTFT", "total_time"
            ]

            # 从time_cost字段中提取（这是实际的位置）
            if "time_cost" in response_data and isinstance(response_data["time_cost"], dict):
                for field in time_fields:
                    if field in response_data["time_cost"]:
                        try:
                            timing_info[field] = float(response_data["time_cost"][field])
                        except (ValueError, TypeError):
                            pass

            # 直接从响应中提取（备用）
            for field in time_fields:
                if field in response_data:
                    try:
                        timing_info[field] = float(response_data[field])
                    except (ValueError, TypeError):
                        pass

            # 从嵌套的time_info中提取（备用）
            if "time_info" in response_data and isinstance(response_data["time_info"], dict):
                for field in time_fields:
                    if field in response_data["time_info"]:
                        try:
                            timing_info[field] = float(response_data["time_info"][field])
                        except (ValueError, TypeError):
                            pass

        return timing_info
    
    def calculate_module_durations(self, timing_info: Dict[str, float]) -> Dict[str, float]:
        """计算各模块的耗时"""
        modules = {}
        
        # 基础时间点
        t1_loc = timing_info.get("t1-loc", 0)
        t2_mem = timing_info.get("t2-mem", 0)
        t3_pre_intent = timing_info.get("t3-pre-intent", 0)
        t3_intent = timing_info.get("t3-intent", 0)
        t4_src = timing_info.get("t4-src", 0)
        t5_fetch = timing_info.get("t5-fetch", 0)
        t6_mft = timing_info.get("t6-mft", 0)
        t7_sft = timing_info.get("t7-sft", 0)
        t8_list = timing_info.get("t8-list", 0)
        t9_brief = timing_info.get("t9-brief", 0)
        t10_detail = timing_info.get("t10-detail", 0)
        ttft = timing_info.get("TTFT", 0)
        
        # 计算各模块耗时
        if t1_loc > 0:
            modules["位置解析"] = t1_loc
        
        if t2_mem > 0 and t1_loc > 0:
            modules["用户画像"] = t2_mem - t1_loc
        
        if t3_intent > 0 and t3_pre_intent > 0:
            modules["意图识别"] = t3_intent - t3_pre_intent
        
        if t4_src > 0 and t3_intent > 0:
            modules["搜索执行"] = t4_src - t3_intent
        
        if t5_fetch > 0 and t4_src > 0:
            modules["内容爬取"] = t5_fetch - t4_src
        
        if t6_mft > 0 and t5_fetch > 0:
            modules["内容总结"] = t6_mft - t5_fetch
        
        if t7_sft > 0 and t6_mft > 0:
            modules["结构化处理"] = t7_sft - t6_mft
        
        if t8_list > 0 and t7_sft > 0:
            modules["列表生成"] = t8_list - t7_sft
        
        if t9_brief > 0 and t8_list > 0:
            modules["简要描述"] = t9_brief - t8_list
        
        if t10_detail > 0 and t9_brief > 0:
            modules["详细描述"] = t10_detail - t9_brief
        
        if ttft > 0:
            modules["首字符时间"] = ttft
        
        return modules
    
    def format_timing_display(self, modules: Dict[str, float], total_time: float) -> str:
        """格式化时间显示"""
        lines = []
        lines.append(f"📊 模块耗时分析 (总时间: {total_time:.2f}s)")
        lines.append("-" * 50)
        
        if not modules:
            lines.append("   ⚠️  未检测到时间数据")
            return "\n".join(lines)
        
        # 按时间排序
        sorted_modules = sorted(modules.items(), key=lambda x: x[1], reverse=True)
        
        for module_name, duration in sorted_modules:
            if duration > 0:
                percentage = (duration / total_time * 100) if total_time > 0 else 0
                bar_length = int(percentage / 5)  # 每5%一个字符
                bar = "█" * bar_length + "░" * (20 - bar_length)
                lines.append(f"   {module_name:12s}: {duration:6.2f}s [{bar}] {percentage:5.1f}%")
        
        return "\n".join(lines)
    
    def test_single_request_timing(self, query: str, description: str) -> Dict[str, Any]:
        """测试单次请求的时间分析"""
        print(f"\n🔍 {description}")
        print(f"   查询: {query}")
        
        payload = {
            "detect": False,
            "engine": "tencent",
            "location": {
                "lat": "31.16813",
                "lon": "121.3999"
            },
            "query": query,
            "stream": False,
            "surrounding": "当前车内有2人，具体为：在二排左位置的儿童车内成员、在主驾位置的未知年龄的车内成员。",
            "use_search_cache": False,
            "user_info": {
                "car_id": "demoCar82952",
                "category": [
                    "natural_landscape_preference",
                    "human_landscape_preference",
                    "entertainment_landscape_preference",
                    "travel_activity"
                ],
                "user_id": "1"
            }
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(
                self.ai_traveler_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            total_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                timing_info = self.extract_detailed_timing(result)
                modules = self.calculate_module_durations(timing_info)
                
                # 显示时间分析
                timing_display = self.format_timing_display(modules, total_time)
                print(timing_display)
                
                # 保存数据用于后续分析
                test_result = {
                    "query": query,
                    "description": description,
                    "total_time": total_time,
                    "timing_info": timing_info,
                    "modules": modules,
                    "success": True
                }
                
                self.timing_data.append(test_result)
                return test_result
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"   ❌ 请求异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_multiple_requests_comparison(self) -> None:
        """测试多次请求的时间对比"""
        print("\n" + "=" * 60)
        print("🔄 多次请求时间对比测试")
        print("=" * 60)
        
        test_queries = [
            ("上海有什么好吃的？", "美食查询"),
            ("北京有什么景点？", "景点查询"),
            ("深圳购物中心推荐", "购物查询"),
            ("杭州周边游玩", "周边游查询"),
            ("成都特色小吃", "特色美食查询")
        ]
        
        for query, desc in test_queries:
            self.test_single_request_timing(query, desc)
            time.sleep(1)  # 避免请求过于频繁
    
    def analyze_timing_statistics(self) -> None:
        """分析时间统计数据"""
        if not self.timing_data:
            print("\n⚠️  没有可分析的时间数据")
            return
        
        print("\n" + "=" * 60)
        print("📈 时间统计分析")
        print("=" * 60)
        
        # 总体统计
        successful_tests = [t for t in self.timing_data if t.get("success", False)]
        if not successful_tests:
            print("❌ 没有成功的测试数据")
            return
        
        total_times = [t["total_time"] for t in successful_tests]
        
        print(f"📊 总体响应时间统计:")
        print(f"   测试次数: {len(successful_tests)}")
        print(f"   平均时间: {mean(total_times):.2f}s")
        print(f"   中位数时间: {median(total_times):.2f}s")
        print(f"   最快时间: {min(total_times):.2f}s")
        print(f"   最慢时间: {max(total_times):.2f}s")
        if len(total_times) > 1:
            print(f"   标准差: {stdev(total_times):.2f}s")
        
        # 模块平均耗时统计
        print(f"\n🔧 各模块平均耗时:")
        all_modules = {}
        
        for test in successful_tests:
            for module_name, duration in test.get("modules", {}).items():
                if module_name not in all_modules:
                    all_modules[module_name] = []
                all_modules[module_name].append(duration)
        
        if all_modules:
            sorted_modules = sorted(all_modules.items(), 
                                  key=lambda x: mean(x[1]), reverse=True)
            
            for module_name, durations in sorted_modules:
                avg_duration = mean(durations)
                print(f"   {module_name:12s}: {avg_duration:6.2f}s (出现{len(durations)}次)")
        else:
            print("   ⚠️  未检测到模块时间数据")
    
    def run_performance_timing_tests(self) -> None:
        """运行性能时间测试"""
        print("=" * 80)
        print("🧪 GAC Agent Traveler 性能时间统计测试 03")
        print(f"🎯 测试目标: {self.base_url}")
        print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 执行多次请求对比测试
        self.test_multiple_requests_comparison()
        
        # 分析统计数据
        self.analyze_timing_statistics()
        
        print("\n" + "=" * 80)
        print("✅ 性能时间统计测试完成")
        print("=" * 80)


def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:8080"
    
    test = PerformanceTimingTest(base_url)
    test.run_performance_timing_tests()


if __name__ == "__main__":
    main()
